-- Migration to fix P&L calculation precision issues
-- This migration updates the trigger function to handle decimal precision properly

-- Drop the existing trigger
DROP TRIGGER IF EXISTS calculate_profit_loss_trigger ON trades;

-- <PERSON>reate updated function with proper decimal precision handling
CREATE OR REPLACE FUNCTION calculate_profit_loss()
RETURNS TRIGGER AS $$
BEGIN
    -- Calculate fees based on trade type
    IF NEW.trade_type = 'option' THEN
        -- Options: $1.30 per contract
        NEW.fees = NEW.contracts * 1.30;
    ELSE
        -- Stocks: No fees
        NEW.fees = 0;
    END IF;

    -- Calculate profit/loss when both entry and exit prices are available
    IF NEW.exit_price IS NOT NULL AND NEW.entry_price IS NOT NULL THEN
        IF NEW.trade_type = 'option' THEN
            -- Options: multiply by 100 for contract multiplier
            NEW.profit_loss = ROUND((NEW.exit_price - NEW.entry_price) * NEW.contracts * 100 - NEW.fees, 2);
        ELSE
            -- Stocks: calculate based on direction (with proper rounding for precision)
            IF NEW.direction = 'long' THEN
                NEW.profit_loss = ROUND((NEW.exit_price - NEW.entry_price) * NEW.shares, 2);
            ELSE -- short
                NEW.profit_loss = ROUND((NEW.entry_price - NEW.exit_price) * NEW.shares, 2);
            END IF;
        END IF;
    END IF;

    -- Calculate R-return for stock trades (risk-reward ratio)
    IF NEW.trade_type = 'stock' AND NEW.stop_price IS NOT NULL AND NEW.entry_price IS NOT NULL THEN
        DECLARE
            risk_amount DECIMAL(10,2);
            reward_amount DECIMAL(10,2);
        BEGIN
            IF NEW.direction = 'long' THEN
                -- Long: risk is entry - stop, reward is exit - entry
                risk_amount = ROUND((NEW.entry_price - NEW.stop_price) * NEW.shares, 2);
                IF NEW.exit_price IS NOT NULL THEN
                    reward_amount = ROUND((NEW.exit_price - NEW.entry_price) * NEW.shares, 2);
                    IF risk_amount > 0 THEN
                        NEW.r_return = ROUND(reward_amount / risk_amount, 4);
                    END IF;
                END IF;
            ELSE -- short
                -- Short: risk is stop - entry, reward is entry - exit
                risk_amount = ROUND((NEW.stop_price - NEW.entry_price) * NEW.shares, 2);
                IF NEW.exit_price IS NOT NULL THEN
                    reward_amount = ROUND((NEW.entry_price - NEW.exit_price) * NEW.shares, 2);
                    IF risk_amount > 0 THEN
                        NEW.r_return = ROUND(reward_amount / risk_amount, 4);
                    END IF;
                END IF;
            END IF;
        END;
    END IF;

    -- Update the updated_at timestamp
    NEW.updated_at = NOW();

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Recreate the trigger
CREATE TRIGGER calculate_profit_loss_trigger
    BEFORE INSERT OR UPDATE ON trades
    FOR EACH ROW
    EXECUTE FUNCTION calculate_profit_loss();

-- Update existing trades to fix any precision issues
UPDATE trades 
SET profit_loss = CASE 
    WHEN trade_type = 'stock' AND exit_price IS NOT NULL AND entry_price IS NOT NULL THEN
        CASE 
            WHEN direction = 'long' THEN ROUND((exit_price - entry_price) * shares, 2)
            ELSE ROUND((entry_price - exit_price) * shares, 2)
        END
    WHEN trade_type = 'option' AND exit_price IS NOT NULL AND entry_price IS NOT NULL THEN
        ROUND((exit_price - entry_price) * contracts * 100 - fees, 2)
    ELSE profit_loss
END
WHERE exit_price IS NOT NULL AND entry_price IS NOT NULL;
