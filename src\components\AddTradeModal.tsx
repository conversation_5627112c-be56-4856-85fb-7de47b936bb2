﻿'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { X } from 'lucide-react'
import { createTrade } from '@/lib/database'
import { format } from 'date-fns'
import OptionTradeForm, { optionTradeSchema, OptionTradeFormData } from './OptionTradeForm'
import StockTradeForm, { stockTradeSchema, StockTradeFormData } from './StockTradeForm'

type TradeType = 'option' | 'stock'

interface AddTradeModalProps {
  isOpen: boolean
  onClose: () => void
  onTradeAdded: () => void
}

export default function AddTradeModal({ isOpen, onClose, onTradeAdded }: AddTradeModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [tradeType, setTradeType] = useState<TradeType>('stock')

  // Option trade form
  const optionForm = useForm<OptionTradeFormData>({
    resolver: zodResolver(optionTradeSchema),
    defaultValues: {
      contracts: 1,
      entry_datetime: format(new Date(), "yyyy-MM-dd'T'HH:mm"),
      option_expiration: format(new Date(), "yyyy-MM-dd"),
    }
  })

  // Stock trade form
  const stockForm = useForm<StockTradeFormData>({
    resolver: zodResolver(stockTradeSchema),
    defaultValues: {
      shares: 100,
      entry_datetime: format(new Date(), "yyyy-MM-dd'T'HH:mm"),
    }
  })

  // Convert datetime-local strings to proper ISO timestamps with timezone
  const convertToISOString = (datetimeLocal: string | null) => {
    if (!datetimeLocal) return null
    const date = new Date(datetimeLocal)
    return date.toISOString()
  }

  const onSubmitOption = async (data: OptionTradeFormData) => {
    try {
      setIsSubmitting(true)

      const tradeData = {
        trade_type: 'option' as const,
        entry_datetime: convertToISOString(data.entry_datetime) || '',
        exit_datetime: data.exit_datetime ? convertToISOString(data.exit_datetime) : null,
        ticker: data.ticker,
        option_type: data.option_type,
        option_strike: data.option_strike,
        option_expiration: data.option_expiration,
        contracts: data.contracts,
        direction: null,
        shares: null,
        stop_price: null,
        entry_price: data.entry_price,
        exit_price: data.exit_price || null,
      }

      await createTrade(tradeData)
      optionForm.reset()
      onClose()
      onTradeAdded()
    } catch (error) {
      console.error('Error creating option trade:', error)
      alert('Failed to create option trade. Please check your input and try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const onSubmitStock = async (data: StockTradeFormData) => {
    try {
      setIsSubmitting(true)

      const tradeData = {
        trade_type: 'stock' as const,
        entry_datetime: convertToISOString(data.entry_datetime) || '',
        exit_datetime: data.exit_datetime ? convertToISOString(data.exit_datetime) : null,
        ticker: data.ticker,
        option_type: null,
        option_strike: null,
        option_expiration: null,
        contracts: null,
        direction: data.direction,
        shares: data.shares,
        stop_price: data.stop_price || null,
        entry_price: data.entry_price,
        exit_price: data.exit_price || null,
      }

      await createTrade(tradeData)
      stockForm.reset()
      onClose()
      onTradeAdded()
    } catch (error) {
      console.error('Error creating stock trade:', error)
      alert('Failed to create stock trade. Please check your input and try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleClose = () => {
    optionForm.reset()
    stockForm.reset()
    onClose()
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-2 sm:p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[95vh] sm:max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-4 sm:p-6 border-b border-gray-200 dark:border-gray-600">
          <h2 className="text-lg sm:text-xl font-semibold text-gray-900 dark:text-white">Add New Trade</h2>
          <button
            type="button"
            onClick={handleClose}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors"
            title="Close modal"
          >
            <X className="w-5 h-5 text-gray-500 dark:text-gray-400" />
          </button>
        </div>

        <div className="p-4 sm:p-6">
          {/* Trade Type Toggle */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              Trade Type
            </label>
            <div className="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
              <button
                type="button"
                onClick={() => setTradeType('stock')}
                className={`flex-1 px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                  tradeType === 'stock'
                    ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                    : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200'
                }`}
              >
                Stock Trade
              </button>
              <button
                type="button"
                onClick={() => setTradeType('option')}
                className={`flex-1 px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                  tradeType === 'option'
                    ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                    : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200'
                }`}
              >
                Option Trade
              </button>
            </div>
          </div>

          {/* Conditional Form Rendering */}
          {tradeType === 'stock' ? (
            <form onSubmit={stockForm.handleSubmit(onSubmitStock)} className="space-y-4">
              <StockTradeForm
                register={stockForm.register}
                watch={stockForm.watch}
                setValue={stockForm.setValue}
                errors={stockForm.formState.errors}
              />

              <div className="flex flex-col sm:flex-row sm:justify-end space-y-2 sm:space-y-0 sm:space-x-3 pt-4 border-t border-gray-200 dark:border-gray-600">
                <button
                  type="button"
                  onClick={handleClose}
                  className="w-full sm:w-auto px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 rounded-md hover:bg-gray-200 dark:hover:bg-gray-500 transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full sm:w-auto px-4 py-2 bg-blue-600 dark:bg-blue-500 text-white rounded-md hover:bg-blue-700 dark:hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {isSubmitting ? 'Adding...' : 'Add Stock Trade'}
                </button>
              </div>
            </form>
          ) : (
            <form onSubmit={optionForm.handleSubmit(onSubmitOption)} className="space-y-4">
              <OptionTradeForm
                register={optionForm.register}
                watch={optionForm.watch}
                setValue={optionForm.setValue}
                errors={optionForm.formState.errors}
              />

              <div className="flex flex-col sm:flex-row sm:justify-end space-y-2 sm:space-y-0 sm:space-x-3 pt-4 border-t border-gray-200 dark:border-gray-600">
                <button
                  type="button"
                  onClick={handleClose}
                  className="w-full sm:w-auto px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 rounded-md hover:bg-gray-200 dark:hover:bg-gray-500 transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full sm:w-auto px-4 py-2 bg-blue-600 dark:bg-blue-500 text-white rounded-md hover:bg-blue-700 dark:hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {isSubmitting ? 'Adding...' : 'Add Option Trade'}
                </button>
              </div>
            </form>
          )}
        </div>
      </div>
    </div>
  )
}
