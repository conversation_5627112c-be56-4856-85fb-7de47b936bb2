﻿'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { X } from 'lucide-react'
import { updateTrade } from '@/lib/database'
import { Trade } from '@/lib/supabase'
import OptionTradeForm, { optionTradeSchema, OptionTradeFormData } from './OptionTradeForm'
import StockTradeForm, { stockTradeSchema, StockTradeFormData } from './StockTradeForm'

interface EditTradeModalProps {
  isOpen: boolean
  onClose: () => void
  onTradeUpdated: () => void
  trade: Trade | null
}

export default function EditTradeModal({ isOpen, onClose, onTradeUpdated, trade }: EditTradeModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Option trade form
  const optionForm = useForm<OptionTradeFormData>({
    resolver: zodResolver(optionTradeSchema),
  })

  // Stock trade form
  const stockForm = useForm<StockTradeFormData>({
    resolver: zodResolver(stockTradeSchema),
  })

  // Convert datetime-local strings to proper ISO timestamps with timezone
  const convertToISOString = (datetimeLocal: string | null) => {
    if (!datetimeLocal) return null
    const date = new Date(datetimeLocal)
    return date.toISOString()
  }

  // Format datetime for input fields
  const formatDateTimeForInput = (dateTime: string | null) => {
    if (!dateTime) return ''
    const date = new Date(dateTime)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    return `${year}-${month}-${day}T${hours}:${minutes}`
  }

  // Populate form when trade changes
  useEffect(() => {
    if (trade && isOpen) {
      if (trade.trade_type === 'option') {
        optionForm.setValue('entry_datetime', formatDateTimeForInput(trade.entry_datetime))
        optionForm.setValue('exit_datetime', formatDateTimeForInput(trade.exit_datetime))
        optionForm.setValue('ticker', trade.ticker)
        optionForm.setValue('option_type', trade.option_type!)
        optionForm.setValue('option_strike', trade.option_strike!)
        optionForm.setValue('option_expiration', trade.option_expiration!)
        optionForm.setValue('contracts', trade.contracts!)
        optionForm.setValue('entry_price', trade.entry_price)
        optionForm.setValue('exit_price', trade.exit_price || undefined)
      } else {
        stockForm.setValue('entry_datetime', formatDateTimeForInput(trade.entry_datetime))
        stockForm.setValue('exit_datetime', formatDateTimeForInput(trade.exit_datetime))
        stockForm.setValue('ticker', trade.ticker)
        stockForm.setValue('direction', trade.direction!)
        stockForm.setValue('shares', trade.shares!)
        stockForm.setValue('entry_price', trade.entry_price)
        stockForm.setValue('exit_price', trade.exit_price || undefined)
        stockForm.setValue('stop_price', trade.stop_price || undefined)
        stockForm.setValue('mfe_price', trade.mfe_price || undefined)
      }
    }
  }, [trade, isOpen, optionForm, stockForm])

  const onSubmitOption = async (data: OptionTradeFormData) => {
    if (!trade) return

    try {
      setIsSubmitting(true)

      const tradeData = {
        entry_datetime: convertToISOString(data.entry_datetime) || '',
        exit_datetime: data.exit_datetime ? convertToISOString(data.exit_datetime) : null,
        ticker: data.ticker,
        option_type: data.option_type,
        option_strike: data.option_strike,
        option_expiration: data.option_expiration,
        contracts: data.contracts,
        entry_price: data.entry_price,
        exit_price: data.exit_price || null,
      }

      await updateTrade(trade.id, tradeData)
      optionForm.reset()
      onClose()
      onTradeUpdated()
    } catch (error) {
      console.error('Error updating option trade:', error)
      alert('Failed to update option trade. Please check your input and try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const onSubmitStock = async (data: StockTradeFormData) => {
    if (!trade) return

    try {
      setIsSubmitting(true)

      const tradeData = {
        entry_datetime: convertToISOString(data.entry_datetime) || '',
        exit_datetime: data.exit_datetime ? convertToISOString(data.exit_datetime) : null,
        ticker: data.ticker,
        direction: data.direction,
        shares: data.shares,
        stop_price: data.stop_price || null,
        mfe_price: data.mfe_price || null,
        entry_price: data.entry_price,
        exit_price: data.exit_price || null,
      }

      await updateTrade(trade.id, tradeData)
      stockForm.reset()
      onClose()
      onTradeUpdated()
    } catch (error) {
      console.error('Error updating stock trade:', error)
      alert('Failed to update stock trade. Please check your input and try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleClose = () => {
    optionForm.reset()
    stockForm.reset()
    onClose()
  }

  if (!isOpen || !trade) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-2 sm:p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[95vh] sm:max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-4 sm:p-6 border-b border-gray-200 dark:border-gray-600">
          <div>
            <h2 className="text-lg sm:text-xl font-semibold text-gray-900 dark:text-white">Edit Trade</h2>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              {trade.trade_type === 'option' ? 'Option Trade' : 'Stock Trade'}
            </p>
          </div>
          <button
            type="button"
            onClick={handleClose}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors"
            title="Close modal"
          >
            <X className="w-5 h-5 text-gray-500 dark:text-gray-400" />
          </button>
        </div>

        <div className="p-4 sm:p-6">
          {trade.trade_type === 'option' ? (
            <form onSubmit={optionForm.handleSubmit(onSubmitOption)} className="space-y-4">
              <OptionTradeForm
                register={optionForm.register}
                watch={optionForm.watch}
                setValue={optionForm.setValue}
                errors={optionForm.formState.errors}
              />
              
              <div className="flex flex-col sm:flex-row sm:justify-end space-y-2 sm:space-y-0 sm:space-x-3 pt-4 border-t border-gray-200 dark:border-gray-600">
                <button
                  type="button"
                  onClick={handleClose}
                  className="w-full sm:w-auto px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 rounded-md hover:bg-gray-200 dark:hover:bg-gray-500 transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full sm:w-auto px-4 py-2 bg-blue-600 dark:bg-blue-500 text-white rounded-md hover:bg-blue-700 dark:hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {isSubmitting ? 'Updating...' : 'Update Option Trade'}
                </button>
              </div>
            </form>
          ) : (
            <form onSubmit={stockForm.handleSubmit(onSubmitStock)} className="space-y-4">
              <StockTradeForm
                register={stockForm.register}
                watch={stockForm.watch}
                setValue={stockForm.setValue}
                errors={stockForm.formState.errors}
              />
              
              <div className="flex flex-col sm:flex-row sm:justify-end space-y-2 sm:space-y-0 sm:space-x-3 pt-4 border-t border-gray-200 dark:border-gray-600">
                <button
                  type="button"
                  onClick={handleClose}
                  className="w-full sm:w-auto px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 rounded-md hover:bg-gray-200 dark:hover:bg-gray-500 transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full sm:w-auto px-4 py-2 bg-blue-600 dark:bg-blue-500 text-white rounded-md hover:bg-blue-700 dark:hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {isSubmitting ? 'Updating...' : 'Update Stock Trade'}
                </button>
              </div>
            </form>
          )}
        </div>
      </div>
    </div>
  )
}
